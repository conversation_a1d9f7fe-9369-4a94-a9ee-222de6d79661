const db = require("../config/db");

const Event = {
  create: async (
    title,
    slug,
    description,
    speaker,
    start_time,
    end_time,
    location,
    image_url,
    author_id
  ) => {
    const [result] = await db.query(
      "INSERT INTO events (title, slug, description, speaker, start_time, end_time, location, image_url, author_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
      [
        title,
        slug,
        description,
        speaker,
        new Date(start_time),
        new Date(end_time),
        location,
        image_url,
        author_id,
      ]
    );
    return result.insertId;
  },

  findAll: async () => {
    const [rows] = await db.query(
      `
      SELECT e.*, u.name as author_name, GROUP_CONCAT(t.name) as tags
      FROM events e
      JOIN users u ON e.author_id = u.id
      LEFT JOIN event_tags et ON e.id = et.event_id
      LEFT JOIN tags t ON et.tag_id = t.id
      WHERE e.start_time >= NOW()
      GROUP BY e.id
      ORDER BY e.start_time ASC
    `
    );
    rows.forEach((row) => {
      row.tags = row.tags ? row.tags.split(",") : [];
    });
    return rows;
  },

  findInRange: async (start, end) => {
    const [rows] = await db.query(
      `
      SELECT e.*, u.name as author_name, GROUP_CONCAT(t.name) as tags
      FROM events e
      JOIN users u ON e.author_id = u.id
      LEFT JOIN event_tags et ON e.id = et.event_id
      LEFT JOIN tags t ON et.tag_id = t.id
      WHERE e.start_time >= ? AND e.start_time <= ?
      GROUP BY e.id
      ORDER BY e.start_time ASC
    `,
      [new Date(start), new Date(end)]
    );
    rows.forEach((row) => {
      row.tags = row.tags ? row.tags.split(",") : [];
    });
    return rows;
  },

  findById: async (id) => {
    const [rows] = await db.query(
      "SELECT e.*, u.name as author_name FROM events e JOIN users u ON e.author_id = u.id WHERE e.id = ?",
      [id]
    );
    return rows[0];
  },

  update: async (
    id,
    title,
    slug,
    description,
    speaker,
    start_time,
    end_time,
    location,
    image_url
  ) => {
    const [result] = await db.query(
      "UPDATE events SET title = ?, slug = ?, description = ?, speaker = ?, start_time = ?, end_time = ?, location = ?, image_url = ? WHERE id = ?",
      [
        title,
        slug,
        description,
        speaker,
        new Date(start_time),
        new Date(end_time),
        location,
        image_url,
        id,
      ]
    );
    return result.affectedRows;
  },

  findBySlug: async (slug) => {
    const [rows] = await db.query(
      `
      SELECT e.*, u.name as author_name, GROUP_CONCAT(t.name) as tags
      FROM events e
      JOIN users u ON e.author_id = u.id
      LEFT JOIN event_tags et ON e.id = et.event_id
      LEFT JOIN tags t ON et.tag_id = t.id
      WHERE e.slug = ?
      GROUP BY e.id
    `,
      [slug]
    );
    if (rows.length > 0) {
      rows[0].tags = rows[0].tags ? rows[0].tags.split(",") : [];
    }
    return rows[0];
  },

  delete: async (id) => {
    const [result] = await db.query("DELETE FROM events WHERE id = ?", [id]);
    return result.affectedRows;
  },

  countUpcoming: async () => {
    const [rows] = await db.query(
      "SELECT COUNT(*) as count FROM events WHERE start_time > NOW()"
    );
    return rows[0].count;
  },
};

module.exports = Event;
