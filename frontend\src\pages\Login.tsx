import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import Logo from "@/assets/logo.png";

const Login = () => {
  const { login, user } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      navigate("/dashboard");
    }
  }, [user, navigate]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await fetch("http://localhost:3001/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || "Login gagal");
      }

      const data = await response.json();
      login(data.token);
      navigate("/dashboard");
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("Login gagal. Periksa kembali email dan password Anda.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className="flex min-h-screen w-full items-center justify-center bg-cover bg-center p-4"
      style={{ backgroundImage: `url('/src/assets/hero-bg.jpg')` }}
    >
      <div className="absolute inset-0 bg-islamic-green/80 backdrop-blur-sm"></div>
      <Card className="relative z-10 w-full max-w-md border-islamic-gold/50 bg-white/90 shadow-2xl">
        <CardHeader className="text-center">
          <img src={Logo} alt="An-Nabawi Logo" className="mx-auto mb-4 h-20" />
          <CardTitle className="font-amiri text-3xl text-islamic-green">
            Masuk Akun
          </CardTitle>
          <CardDescription className="text-gray-600">
            Masuk ke akun An-Nabawi Anda untuk melanjutkan
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="email" className="text-islamic-green">
                  Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Masukkan email Anda"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="border-islamic-green/50 focus:border-islamic-green focus:ring-islamic-gold"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password" className="text-islamic-green">
                  Password
                </Label>
                <Input
                  id="password"
                  type="password"
                  required
                  placeholder="Masukkan password Anda"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="border-islamic-green/50 focus:border-islamic-green focus:ring-islamic-gold"
                />
              </div>
              {error && (
                <p className="text-red-500 text-sm text-center">{error}</p>
              )}
              <Button
                type="submit"
                variant="islamic"
                className="w-full"
                disabled={loading}
              >
                {loading ? "Loading..." : "Login"}
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="mt-4 text-center text-sm text-gray-600">
            Belum punya akun?{" "}
            <Link
              to="/register"
              className="font-semibold text-islamic-green hover:text-islamic-gold hover:underline"
            >
              Register
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Login;
