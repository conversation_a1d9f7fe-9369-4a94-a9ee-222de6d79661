import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/sonner";
import { Heart, DollarSign, Users, TrendingUp, LogIn } from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Link } from "react-router-dom";

interface DonationHistory {
  id: number;
  type: string;
  amount: number;
  created_at: string;
  message?: string;
}

const GuestHistoryWidget = () => {
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [rows, setRows] = useState<DonationHistory[]>([]);
  const [loading, setLoading] = useState(false);

  const load = async () => {
    if (!email || !phone) {
      toast("Error", { description: "Email dan nomor HP harus diisi" });
      return;
    }

    try {
      setLoading(true);
      const res = await fetch(
        `http://localhost:3001/api/ziswaf/guest/history?email=${encodeURIComponent(
          email
        )}&phone=${encodeURIComponent(phone)}`
      );
      const data = await res.json();
      setRows(Array.isArray(data) ? data : []);

      if (data.length === 0) {
        toast("Info", { description: "Tidak ada riwayat donasi ditemukan" });
      }
    } catch (error) {
      toast("Error", { description: "Gagal memuat riwayat" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-3">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <Input
          placeholder="Email yang digunakan saat donasi"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          type="email"
        />
        <Input
          placeholder="Nomor HP yang digunakan saat donasi"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
        />
      </div>
      <Button
        onClick={load}
        disabled={loading || !email || !phone}
        className="w-full"
      >
        {loading ? "Mencari..." : "Cek Riwayat"}
      </Button>
      <div className="space-y-2">
        {rows.length === 0 ? (
          <div className="text-muted-foreground text-sm">Belum ada data.</div>
        ) : (
          rows.map((r) => (
            <div key={r.id} className="border rounded-md p-3 text-sm">
              <div>
                <b>{r.type}</b> - Rp{Number(r.amount).toLocaleString()}
              </div>
              <div className="text-muted-foreground">
                {new Date(r.created_at).toLocaleString()}
              </div>
              {r.message && <div className="mt-1">“{r.message}”</div>}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

const Ziswaf = () => {
  const [form, setForm] = useState({
    type: "INFAQ",
    amount: "",
    name: "",
    email: "",
    phone: "",
    message: "",
    payment_method: "QRIS",
  });
  const [loading, setLoading] = useState(false);

  const submit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      const res = await fetch("http://localhost:3001/api/ziswaf/donations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          type: form.type,
          amount: Number(form.amount),
          name: form.name || undefined,
          email: form.email || undefined,
          phone: form.phone || undefined,
          message: form.message || undefined,
          payment_method: form.payment_method,
        }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.message || "Gagal mengirim donasi");
      toast("Terima kasih!", {
        description:
          "Donasi Anda telah kami terima dan sedang diproses. Mohon selesaikan pembayaran jika belum.",
      });
      setForm({
        type: "INFAQ",
        amount: "",
        name: "",
        email: "",
        phone: "",
        message: "",
        payment_method: "QRIS",
      });
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast("Error", { description: err.message });
      } else {
        toast("Error", {
          description: "Terjadi kesalahan yang tidak diketahui",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen font-inter bg-gradient-to-br from-islamic-green/5 to-islamic-gold/5">
      <Navigation />

      <div className="pt-20 pb-16">
        <div className="container mx-auto px-4 py-12 max-w-6xl">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-12 h-12 bg-islamic-green rounded-full flex items-center justify-center">
                <Heart className="h-6 w-6 text-white" />
              </div>
              <h1 className="font-poppins text-4xl text-islamic-green font-bold">
                ZISWAF
              </h1>
            </div>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Zakat, Infaq, Sedekah, dan Wakaf - Mari bersama membangun kebaikan
              dan keberkahan
            </p>
          </div>

          {/* Info Cards */}
          <div className="grid md:grid-cols-4 gap-6 mb-12">
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <DollarSign className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-islamic-green mb-2">Zakat</h3>
                <p className="text-sm text-muted-foreground">
                  Kewajiban bagi Muslim yang memenuhi nisab
                </p>
              </CardContent>
            </Card>
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-islamic-green mb-2">Infaq</h3>
                <p className="text-sm text-muted-foreground">
                  Mengeluarkan harta untuk kebaikan
                </p>
              </CardContent>
            </Card>
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Heart className="h-6 w-6 text-yellow-600" />
                </div>
                <h3 className="font-semibold text-islamic-green mb-2">
                  Sedekah
                </h3>
                <p className="text-sm text-muted-foreground">
                  Pemberian sukarela untuk sesama
                </p>
              </CardContent>
            </Card>
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-islamic-green mb-2">Wakaf</h3>
                <p className="text-sm text-muted-foreground">
                  Menahan harta untuk kemaslahatan umum
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Form Donasi</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={submit} className="space-y-4">
                  <div>
                    <Label>Jenis</Label>
                    <select
                      className="w-full border rounded-md p-2"
                      value={form.type}
                      onChange={(e) =>
                        setForm({ ...form, type: e.target.value })
                      }
                    >
                      <option value="ZAKAT">Zakat</option>
                      <option value="INFAQ">Infaq</option>
                      <option value="SEDEKAH">Sedekah</option>
                      <option value="WAKAF">Wakaf</option>
                    </select>
                  </div>
                  <div>
                    <Label>Nominal</Label>
                    <Input
                      type="number"
                      value={form.amount}
                      onChange={(e) =>
                        setForm({ ...form, amount: e.target.value })
                      }
                      required
                    />
                  </div>
                  <div>
                    <Label>Metode Pembayaran</Label>
                    <select
                      className="w-full border rounded-md p-2"
                      value={form.payment_method}
                      onChange={(e) =>
                        setForm({ ...form, payment_method: e.target.value })
                      }
                    >
                      <option value="QRIS">QRIS</option>
                      <option value="TRANSFER_BSI">Transfer Bank (BSI)</option>
                    </select>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>Nama (opsional jika login)</Label>
                      <Input
                        value={form.name}
                        onChange={(e) =>
                          setForm({ ...form, name: e.target.value })
                        }
                      />
                    </div>
                    <div>
                      <Label>Email (opsional jika login)</Label>
                      <Input
                        type="email"
                        value={form.email}
                        onChange={(e) =>
                          setForm({ ...form, email: e.target.value })
                        }
                      />
                    </div>
                    <div>
                      <Label>No HP (opsional jika login)</Label>
                      <Input
                        value={form.phone}
                        onChange={(e) =>
                          setForm({ ...form, phone: e.target.value })
                        }
                      />
                    </div>
                    <div>
                      <Label>Pesan</Label>
                      <Input
                        value={form.message}
                        onChange={(e) =>
                          setForm({ ...form, message: e.target.value })
                        }
                      />
                    </div>
                  </div>
                  <Button type="submit" disabled={loading}>
                    {loading ? "Mengirim..." : "Kirim Donasi"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Riwayat Guest (Cek via Email + HP)</CardTitle>
              </CardHeader>
              <CardContent>
                <GuestHistoryWidget />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Ziswaf;
