import React from "react";
import { Outlet, <PERSON>, useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Users,
  Newspaper,
  Calendar,
  PlusCircle,
  LayoutList,
  Home,
  PanelLeft,
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import Logo from "@/assets/logo-01.png";

const AppHeader = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const getInitials = (name?: string | null) => {
    if (!name) return "AD";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <header className="sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-4 sm:static sm:h-auto sm:border-0 sm:bg-transparent sm:px-6">
      <MobileSidebar />
      <div className="flex-1" />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            className="overflow-hidden rounded-full w-8 h-8 mt-2"
          >
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={`https://api.dicebear.com/8.x/initials/svg?seed=${user?.name}`}
                alt={user?.name || "User"}
              />
              <AvatarFallback className="text-xs">
                {getInitials(user?.name)}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>My Account</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link to="/dashboard/settings">Settings</Link>
          </DropdownMenuItem>
          <DropdownMenuItem>Support</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout}>Logout</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </header>
  );
};

const DesktopSidebar = () => (
  <div className="hidden border-r bg-muted/40 md:block">
    <div className="flex h-full max-h-screen flex-col gap-2">
      <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
        <Link to="/" className="flex items-center gap-2 font-semibold">
          <img src={Logo} alt="An-Nabawi Logo" className="h-8" />
          <span className="font-amiri text-xl text-islamic-green">
            An-Nabawi
          </span>
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-2">
        <nav className="grid items-start px-2 text-sm font-medium lg:px-4">
          <SidebarNavLinks />
        </nav>
      </div>
    </div>
  </div>
);

const MobileSidebar = () => (
  <Sheet>
    <SheetTrigger asChild>
      <Button size="icon" variant="outline" className="sm:hidden">
        <PanelLeft className="h-5 w-5" />
        <span className="sr-only">Toggle Menu</span>
      </Button>
    </SheetTrigger>
    <SheetContent side="left" className="sm:max-w-xs">
      <nav className="grid gap-6 text-lg font-medium">
        <Link
          to="/"
          className="group flex h-10 w-10 shrink-0 items-center justify-center gap-2 rounded-full bg-primary text-lg font-semibold text-primary-foreground md:text-base"
        >
          <img src={Logo} alt="An-Nabawi Logo" className="h-6" />
          <span className="sr-only">An-Nabawi</span>
        </Link>
        <SidebarNavLinks />
      </nav>
    </SheetContent>
  </Sheet>
);

const SidebarNavLinks = () => {
  const { user } = useAuth();
  const isAdmin = user?.roles.includes("admin");
  const isArticleCreator = user?.roles.includes("article_creator");
  const isEventCreator = user?.roles.includes("event_creator");

  return (
    <>
      <Link
        to="/dashboard"
        className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
      >
        <Home className="h-4 w-4" />
        Dashboard
      </Link>
      {isAdmin && (
        <Link
          to="/dashboard/users"
          className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
        >
          <Users className="h-4 w-4" />
          User Management
        </Link>
      )}
      <Accordion type="single" collapsible className="w-full">
        {(isAdmin || isArticleCreator) && (
          <AccordionItem value="item-1" className="border-b-0">
            <AccordionTrigger className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary hover:no-underline">
              <Newspaper className="h-4 w-4" />
              Articles
            </AccordionTrigger>
            <AccordionContent className="pl-8">
              <Link
                to="/dashboard/articles/new"
                className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
              >
                <PlusCircle className="h-4 w-4" />
                Add New
              </Link>
              <Link
                to="/dashboard/articles"
                className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
              >
                <LayoutList className="h-4 w-4" />
                Article List
              </Link>
            </AccordionContent>
          </AccordionItem>
        )}
        {(isAdmin || isEventCreator) && (
          <AccordionItem value="item-2" className="border-b-0">
            <AccordionTrigger className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary hover:no-underline">
              <Calendar className="h-4 w-4" />
              Acara / Kajian
            </AccordionTrigger>
            <AccordionContent className="pl-8">
              <Link
                to="/dashboard/events/new"
                className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
              >
                <PlusCircle className="h-4 w-4" />
                Add New
              </Link>
              <Link
                to="/dashboard/events"
                className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
              >
                <LayoutList className="h-4 w-4" />
                Event List
              </Link>
            </AccordionContent>
          </AccordionItem>
        )}
      </Accordion>
    </>
  );
};

const DashboardLayout = () => {
  return (
    <div className="grid min-h-screen w-full md:grid-cols-[220px_1fr] lg:grid-cols-[280px_1fr] font-inter">
      <DesktopSidebar />
      <div className="flex flex-col">
        <AppHeader />
        <main className="flex flex-1 flex-col gap-4 p-4 lg:gap-6 lg:p-6">
          <Outlet />
        </main>
        <footer className="mt-auto border-t bg-background px-6 py-4 text-center text-sm text-muted-foreground">
          © {new Date().getFullYear()} An-Nabawi Islamic Center. All rights
          reserved.
        </footer>
      </div>
    </div>
  );
};

export default DashboardLayout;
