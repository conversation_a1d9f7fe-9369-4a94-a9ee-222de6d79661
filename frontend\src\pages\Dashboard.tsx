import React, { useState, useEffect } from "react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Newspaper, Calendar, PlusCircle, Heart } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { Skeleton } from "@/components/ui/skeleton";

interface Stats {
  totalArticles: number;
  upcomingEvents: number;
}

const AdminDashboard = () => {
  const [stats, setStats] = useState<Stats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { token } = useAuth();

  useEffect(() => {
    const fetchStats = async () => {
      if (!token) return;
      setIsLoading(true);
      try {
        const response = await fetch(
          "http://localhost:3001/api/dashboard/stats",
          {
            headers: { Authorization: `Bear<PERSON> ${token}` },
          }
        );
        if (!response.ok) throw new Error("Failed to fetch stats");
        const data = await response.json();
        setStats(data);
      } catch (error) {
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchStats();
  }, [token]);

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card className="col-span-full">
        <CardHeader>
          <CardTitle className="font-amiri text-2xl text-islamic-green">
            Selamat Datang di Dasbor Admin
          </CardTitle>
          <CardDescription>
            Pilih salah satu menu di sidebar untuk mulai mengelola konten.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p>
            Anda dapat mengelola pengguna, artikel berita, dan acara kajian dari
            sini. Gunakan menu navigasi di sebelah kiri untuk memulai.
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Artikel</CardTitle>
          <Newspaper className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-1/4" />
          ) : (
            <div className="text-2xl font-bold">
              +{stats?.totalArticles ?? 0}
            </div>
          )}
          <p className="text-xs text-muted-foreground">
            Total artikel yang dipublikasikan
          </p>
        </CardContent>
        <CardContent>
          <Button size="sm" className="w-full" asChild>
            <Link to="/dashboard/articles/new">
              <PlusCircle className="mr-2 h-4 w-4" /> Tambah Artikel Baru
            </Link>
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Acara / Kajian</CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-1/4" />
          ) : (
            <div className="text-2xl font-bold">
              +{stats?.upcomingEvents ?? 0}
            </div>
          )}
          <p className="text-xs text-muted-foreground">
            Acara yang akan datang
          </p>
        </CardContent>
        <CardContent>
          <Button size="sm" className="w-full" asChild>
            <Link to="/dashboard/events/new">
              <PlusCircle className="mr-2 h-4 w-4" /> Tambah Acara Baru
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

const MemberDashboard = () => {
  const { user } = useAuth();
  return (
    <div className="grid gap-4">
      <Card>
        <CardHeader>
          <CardTitle className="font-amiri text-2xl text-islamic-green">
            Selamat Datang, {user?.name}!
          </CardTitle>
          <CardDescription>
            Ini adalah halaman dasbor pribadi Anda.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            Terima kasih telah menjadi bagian dari komunitas Masjid An-Nabawi.
            Jelajahi fitur yang tersedia untuk Anda.
          </p>
          <Button asChild>
            <Link to="/ziswaf">
              <Heart className="mr-2 h-4 w-4" /> Lakukan Donasi ZISWAF
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

const Dashboard = () => {
  const { user } = useAuth();

  const isAdmin = user?.roles.includes("admin");
  const isCreator =
    user?.roles.includes("article_creator") ||
    user?.roles.includes("event_creator");

  if (isAdmin || isCreator) {
    return <AdminDashboard />;
  }

  return <MemberDashboard />;
};

export default Dashboard;
