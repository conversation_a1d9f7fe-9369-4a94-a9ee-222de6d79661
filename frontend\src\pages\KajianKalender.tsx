import { useEffect, useState } from "react";
import { Calendar as CalendarIcon, Clock, MapPin, User } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";

interface EventItem {
  id: number;
  title: string;
  speaker: string;
  start_time: string;
  end_time: string;
  location: string;
  description: string;
}

const KajianKalender = () => {
  const [selected, setSelected] = useState<Date | undefined>(new Date());
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [events, setEvents] = useState<EventItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<"single" | "range">("single");

  const start =
    viewMode === "range" && dateRange?.from
      ? format(dateRange.from, "yyyy-MM-dd")
      : selected
      ? format(selected, "yyyy-MM-dd")
      : undefined;
  const end =
    viewMode === "range" && dateRange?.to
      ? format(dateRange.to, "yyyy-MM-dd")
      : selected
      ? format(selected, "yyyy-MM-dd")
      : undefined;

  useEffect(() => {
    const load = async () => {
      try {
        setLoading(true);
        const url =
          start && end
            ? `http://localhost:3001/api/events?start=${start}&end=${end}`
            : `http://localhost:3001/api/events`;
        const res = await fetch(url);
        const data = await res.json();
        setEvents(data);
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [start, end]);

  return (
    <div className="min-h-screen font-inter bg-gradient-to-br from-islamic-green/5 to-islamic-gold/5">
      <Navigation />

      <div className="pt-20 pb-16">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center mb-10">
            <div className="flex items-center justify-center gap-3 mb-3">
              <div className="w-12 h-12 bg-islamic-green rounded-full flex items-center justify-center">
                <CalendarIcon className="h-6 w-6 text-white" />
              </div>
              <h1 className="font-poppins font-bold text-3xl md:text-4xl text-islamic-green">
                Kalender Kajian & Acara
              </h1>
            </div>
            <p className="text-muted-foreground">
              Pilih tanggal atau rentang tanggal untuk melihat kajian
            </p>
          </div>

          <div className="max-w-6xl mx-auto mb-8">
            <div className="flex justify-center gap-4 mb-6">
              <Button
                variant={viewMode === "single" ? "islamic" : "outline"}
                onClick={() => setViewMode("single")}
              >
                Pilih Tanggal
              </Button>
              <Button
                variant={viewMode === "range" ? "islamic" : "outline"}
                onClick={() => setViewMode("range")}
              >
                Rentang Tanggal
              </Button>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-islamic-green">
                  {viewMode === "single"
                    ? "Pilih Tanggal"
                    : "Pilih Rentang Tanggal"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {viewMode === "single" ? (
                  <Calendar
                    mode="single"
                    selected={selected}
                    onSelect={setSelected}
                    className="rounded-md border w-full"
                  />
                ) : (
                  <Calendar
                    mode="range"
                    selected={dateRange}
                    onSelect={setDateRange}
                    className="rounded-md border w-full"
                  />
                )}
              </CardContent>
            </Card>

            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-islamic-green">
                  {viewMode === "single" && selected
                    ? `Jadwal ${format(selected, "EEEE, d MMM yyyy")}`
                    : viewMode === "range" && dateRange?.from && dateRange?.to
                    ? `Jadwal ${format(dateRange.from, "d MMM")} - ${format(
                        dateRange.to,
                        "d MMM yyyy"
                      )}`
                    : "Semua Jadwal"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-islamic-green"></div>
                  </div>
                ) : events.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Tidak ada kajian pada periode ini.
                  </div>
                ) : (
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {events.map((e) => (
                      <Card
                        key={e.id}
                        className="border-l-4 border-l-islamic-green"
                      >
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <h3 className="font-semibold text-islamic-green">
                              {e.title}
                            </h3>
                            <Badge variant="secondary" className="text-xs">
                              {format(new Date(e.start_time), "d MMM")}
                            </Badge>
                          </div>
                          <div className="space-y-2 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              <span>{e.speaker}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4" />
                              <span>
                                {format(new Date(e.start_time), "HH:mm")} -{" "}
                                {format(new Date(e.end_time), "HH:mm")}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4" />
                              <span>{e.location}</span>
                            </div>
                          </div>
                          <p className="text-sm mt-3 text-foreground">
                            {e.description}
                          </p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default KajianKalender;
