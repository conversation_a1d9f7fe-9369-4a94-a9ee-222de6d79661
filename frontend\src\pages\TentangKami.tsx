import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Target, Heart, MapPin, Mail, Phone } from "lucide-react";

const TentangKami = () => {
  return (
    <div className="min-h-screen font-inter bg-gradient-to-br from-islamic-green/5 to-islamic-gold/5">
      <Navigation />

      <div className="pt-20 pb-16">
        <div className="container mx-auto px-4 py-12 max-w-6xl space-y-12">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-12 h-12 bg-islamic-green rounded-full flex items-center justify-center">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h1 className="font-poppins text-4xl text-islamic-green font-bold">
                Tentang <PERSON>
              </h1>
            </div>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              An-Nabawi Islamic Center berkomitmen menjadi pusat dakwah,
              pendidikan, dan pelayanan umat yang rahmatan lil'alamin.
            </p>
          </div>

          {/* Cards */}
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="shadow-lg">
              <CardHeader className="bg-islamic-green text-white rounded-t-lg">
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Visi Kami
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-lg leading-relaxed">
                  Menjadi pusat aktivitas keislaman yang unggul, inklusif, dan
                  berdampak positif bagi masyarakat luas dengan menjunjung
                  tinggi nilai-nilai Islam yang rahmatan lil'alamin.
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-lg">
              <CardHeader className="bg-islamic-green text-white rounded-t-lg">
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5" />
                  Misi Kami
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-islamic-green rounded-full mt-2 flex-shrink-0"></div>
                    <span>
                      Menyelenggarakan kajian ilmu Islam yang berkualitas dan
                      mudah dipahami
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-islamic-green rounded-full mt-2 flex-shrink-0"></div>
                    <span>
                      Menguatkan ukhuwah melalui program sosial dan pendidikan
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-islamic-green rounded-full mt-2 flex-shrink-0"></div>
                    <span>
                      Mengelola donasi ZISWAF secara transparan dan akuntabel
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-islamic-green rounded-full mt-2 flex-shrink-0"></div>
                    <span>
                      Memberdayakan masyarakat melalui program-program
                      kemanusiaan
                    </span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Contact Info */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-islamic-green text-center">
                Kontak & Lokasi
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid md:grid-cols-3 gap-6 text-center">
                <div className="flex flex-col items-center space-y-3">
                  <div className="w-12 h-12 bg-islamic-green/10 rounded-full flex items-center justify-center">
                    <MapPin className="h-6 w-6 text-islamic-green" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-islamic-green mb-1">
                      Alamat
                    </h3>
                    <p className="text-muted-foreground">
                      Jl. Contoh No. 123
                      <br />
                      Kota Contoh, Indonesia
                    </p>
                  </div>
                </div>

                <div className="flex flex-col items-center space-y-3">
                  <div className="w-12 h-12 bg-islamic-green/10 rounded-full flex items-center justify-center">
                    <Mail className="h-6 w-6 text-islamic-green" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-islamic-green mb-1">
                      Email
                    </h3>
                    <p className="text-muted-foreground">
                      <EMAIL>
                    </p>
                  </div>
                </div>

                <div className="flex flex-col items-center space-y-3">
                  <div className="w-12 h-12 bg-islamic-green/10 rounded-full flex items-center justify-center">
                    <Phone className="h-6 w-6 text-islamic-green" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-islamic-green mb-1">
                      Telepon
                    </h3>
                    <p className="text-muted-foreground">0812-3456-7890</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default TentangKami;
