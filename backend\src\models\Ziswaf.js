const db = require("../config/db");

const Ziswaf = {
  createDonation: async ({
    user_id = null,
    guest_name,
    guest_email,
    guest_phone,
    type,
    amount,
    message,
    payment_method,
  }) => {
    const [result] = await db.query(
      `INSERT INTO donations (user_id, guest_name, guest_email, guest_phone, type, amount, message, payment_method)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        user_id,
        guest_name,
        guest_email,
        guest_phone,
        type,
        amount,
        message || null,
        payment_method,
      ]
    );
    return result.insertId;
  },

  getUserDonations: async (userId) => {
    const [rows] = await db.query(
      `SELECT * FROM donations WHERE user_id = ? ORDER BY created_at DESC`,
      [userId]
    );
    return rows;
  },

  getGuestDonations: async ({ email, phone }) => {
    const [rows] = await db.query(
      `SELECT * FROM donations WHERE guest_email = ? AND guest_phone = ? ORDER BY created_at DESC`,
      [email, phone]
    );
    return rows;
  },

  getFinanceSummary: async (period = "12") => {
    const [byType] = await db.query(
      `SELECT type, SUM(amount) as total FROM donations GROUP BY type`
    );

    let monthsLimit = 12;
    let dateFilter = "";

    switch (period) {
      case "1":
        monthsLimit = 1;
        dateFilter = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
        break;
      case "3":
        monthsLimit = 3;
        dateFilter = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH)";
        break;
      case "6":
        monthsLimit = 6;
        dateFilter = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)";
        break;
      case "12":
      default:
        monthsLimit = 12;
        dateFilter = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)";
        break;
    }

    const [byMonth] = await db.query(
      `SELECT DATE_FORMAT(created_at, '%Y-%m') as month, SUM(amount) as total
       FROM donations
       ${dateFilter}
       GROUP BY month
       ORDER BY month DESC
       LIMIT ${monthsLimit}`
    );

    const [totalAmount] = await db.query(
      `SELECT SUM(amount) as total FROM donations ${dateFilter}`
    );

    return { byType, byMonth, totalAmount: totalAmount[0]?.total || 0, period };
  },
};

module.exports = Ziswaf;
