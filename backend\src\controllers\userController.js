const User = require("../models/User");
const jwt = require("jsonwebtoken");

exports.getAllUsers = async (req, res) => {
  try {
    const users = await User.findAll();
    res.json(users);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

exports.createUser = async (req, res) => {
  const { name, email, phone, password, roleIds } = req.body;
  try {
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json({ message: "Email already exists" });
    }
    const userId = await User.create(name, email, phone, password, roleIds);
    res.status(201).json({ message: "User created successfully", userId });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

exports.updateUser = async (req, res) => {
  const { id } = req.params;
  const { name, email, phone, password, roleIds } = req.body;
  const loggedInUser = req.user;

  // Otorisasi: Hanya admin atau pengguna itu sendiri yang dapat memperbarui
  if (
    loggedInUser.id !== parseInt(id) &&
    !loggedInUser.roles.includes("admin")
  ) {
    return res.status(403).json({ message: "User not authorized" });
  }

  // Hanya admin yang dapat mengubah peran
  const rolesToUpdate = loggedInUser.roles.includes("admin")
    ? roleIds
    : undefined;

  try {
    // Cek jika email baru sudah digunakan oleh user lain
    const userByEmail = await User.findByEmail(email);
    if (userByEmail && userByEmail.id !== parseInt(id)) {
      return res
        .status(400)
        .json({ message: "Email is already in use by another account." });
    }

    await User.update(id, name, email, phone, password, rolesToUpdate);

    res.json({ message: "User updated successfully" });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

exports.deleteUser = async (req, res) => {
  const { id } = req.params;
  try {
    await User.delete(id);
    res.json({ message: "User deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};
